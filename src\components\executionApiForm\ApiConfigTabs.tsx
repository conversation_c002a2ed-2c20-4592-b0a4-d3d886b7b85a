import React, { useEffect } from 'react';
import { Box, Flex } from '@mantine/core';
import { Controller, UseFormReturn, useFieldArray, useWatch } from 'react-hook-form';
import { KanbanTabs, KanbanText, KanbanSelect, KanbanSwitch, KanbanButton } from 'kanban-design-system';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import {
  AuthTypeEnum,
  BodyTypeEnum,
  ContentTypeEnum,
  CONTENT_TYPE_OPTIONS,
  getContentTypeValue,
  getFormUrlencodedContentType,
  HTTP_VERSION_OPTIONS,
  optionAuthTypes,
} from '@common/constants/ExecutionConstants';
import ParamAwareInput from '@pages/admins/executionConfig/tabs/execution/components/ParamAwareInput';
import { ParamAwareTextarea } from '@pages/admins/executionConfig/tabs/execution/components/ParamAwareTextarea';
import classes from '../../pages/admins/executionConfig/tabs/execution/ApiConfigSession.module.css';
import { upsertAutoHeaderForApiInfo } from './utils/ApiFormUtils';
import { EXECUTION_API_AUTHORIZATION, EXECUTION_API_CONTENT_TYPE } from '@pages/admins/executionConfig/Constants';
import { generateAuthHeader } from '@common/utils/ExecutionApiUtils';
import { AuthTab } from './tabs/AuthTab';
import { HeadersTab } from './tabs/HeadersTab';
import { ParamsTab } from './tabs/ParamsTab';
import { BodyTab } from './tabs/BodyTab';
import SettingsTab from './tabs/SettingsTab';

interface ApiConfigTabsProps {
  form: UseFormReturn<{ apiInfo: ExecutionApiInfoModel }>;
  isViewMode: boolean;
  variableNames: string[];
  textareaRef: React.MutableRefObject<HTMLElement | null>;
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
}

export const ApiConfigTabs: React.FC<ApiConfigTabsProps> = ({ form, isViewMode, registerEditor, variableNames }) => {
  const { control, setValue, getValues, setFocus } = form;

  const paramsFA = useFieldArray({ control, name: 'apiInfo.params' });
  const headersFA = useFieldArray({ control, name: 'apiInfo.headers' });
  const formFA = useFieldArray({ control, name: 'apiInfo.body.formUrlEncoded' });

  const authType = useWatch({ control, name: 'apiInfo.authentication.authType' });
  const authToken = useWatch({ control, name: 'apiInfo.authentication.token' });
  const authUsername = useWatch({ control, name: 'apiInfo.authentication.username' });
  const authPassword = useWatch({ control, name: 'apiInfo.authentication.password' });
  const bodyType = useWatch({ control, name: 'apiInfo.body.bodyType' });
  const selectedContentType = useWatch({ control, name: 'apiInfo.body.contentType' });

  // Auto-generate Authorization header when auth changes
  useEffect(() => {
    if (isViewMode) {
      return;
    }

    const authValue = generateAuthHeader(authType, {
      token: authToken,
      username: authUsername,
      password: authPassword,
    });

    if (authValue) {
      upsertAutoHeaderForApiInfo({
        key: EXECUTION_API_AUTHORIZATION,
        value: authValue,
        headersFA,
        setValue,
      });
    } else {
      const existing = headersFA.fields.findIndex((h: any) => h.key?.toLowerCase() === EXECUTION_API_AUTHORIZATION.toLowerCase() && h.autoGenerated);
      if (existing >= 0) {
        headersFA.remove(existing);
      }
    }
  }, [authType, authToken, authUsername, authPassword, isViewMode, headersFA, setValue]);

  // Auto-generate Content-Type header when body type or content type changes
  useEffect(() => {
    if (isViewMode) {
      return;
    }

    let contentTypeValue: string = '';

    if (bodyType === BodyTypeEnum.RAW && selectedContentType) {
      contentTypeValue = getContentTypeValue(selectedContentType);
    } else if (bodyType === BodyTypeEnum.URLENCODED) {
      contentTypeValue = getFormUrlencodedContentType();
    }

    if (contentTypeValue !== '') {
      upsertAutoHeaderForApiInfo({
        key: EXECUTION_API_CONTENT_TYPE,
        value: contentTypeValue,
        headersFA,
        setValue,
      });
    } else {
      const existing = headersFA.fields.findIndex((h: any) => h.key?.toLowerCase() === EXECUTION_API_CONTENT_TYPE.toLowerCase() && h.autoGenerated);
      if (existing >= 0) {
        headersFA.remove(existing);
      }
    }
  }, [bodyType, selectedContentType, isViewMode, headersFA, setValue]);

  return (
    <KanbanTabs
      configs={{ defaultValue: 'params', classNames: { root: classes.tabsRoot, list: classes.tabsList } }}
      tabs={{
        params: {
          title: 'Params',
          content: (
            <ParamsTab
              control={control}
              setValue={setValue}
              getValues={getValues}
              setFocus={setFocus}
              paramsFA={paramsFA}
              isViewMode={isViewMode}
              variableNames={variableNames}
              registerEditor={registerEditor}
            />
          ),
        },
        auth: {
          title: 'Authorization',
          content: (
            <AuthTab
              control={control}
              setValue={setValue}
              getValues={getValues}
              headersFA={headersFA}
              isViewMode={isViewMode}
              variableNames={variableNames}
            />
          ),
        },
        headers: {
          title: 'Headers',
          content: (
            <HeadersTab
              control={control}
              setValue={setValue}
              getValues={getValues}
              setFocus={setFocus}
              headersFA={headersFA}
              isViewMode={isViewMode}
              variableNames={variableNames}
              registerEditor={registerEditor}
            />
          ),
        },
        body: {
          title: 'Body',
          content: (
            <BodyTab
              control={control}
              setValue={setValue}
              getValues={getValues}
              setFocus={setFocus}
              headersFA={headersFA}
              formFA={formFA}
              isViewMode={isViewMode}
              variableNames={variableNames}
              registerEditor={registerEditor}
            />
          ),
        },
        settings: {
          title: 'Settings',
          content: (
            <SettingsTab
              control={control}
              setValue={setValue}
              getValues={getValues}
              isViewMode={isViewMode}
            />
          ),
        },
      }}
    />
  );
};

export default ApiConfigTabs;
